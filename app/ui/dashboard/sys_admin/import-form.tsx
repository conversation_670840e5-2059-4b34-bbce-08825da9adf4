'use client';

import { useState, useEffect } from 'react';
import { ArrowUpTrayIcon } from '@heroicons/react/24/outline';
import * as XLSX from 'xlsx';

interface ColumnMapping {
  [key: string]: string;
}

interface ExcelData {
  headers: string[];
  firstRow: any;
}

// Pre-defined column mappings based on common Excel column names
const COLUMN_MAPPINGS: { [key: string]: string } = {
  // Basic Information
  'Employee ID': 'employee_id',
  'SSO ID': 'sso_id',
  'First Name': 'first_name',
  'Last Name': 'last_name',
  'Middle Initial': 'middle_initial',
  'Preferred Name': 'preferred_name',
  'Suffix': 'suffix',
  'Work Email': 'work_email',
  'Personal Email': 'personal_email',
  'Honorific': 'honorific',
  
  // Employment Information
  'Employment Status': 'employment_status',
  'Date Started': 'date_started',
  'Rank': 'rank_name',
  'Tenure Status': 'tenure_status',
  'Primary Unit': 'primary_unit_id',
  'Primary Unit Percentage': 'primary_unit_percentage',
  'Primary Unit Sub Group': 'primary_unit_sub_group',
  
  // Contact Information
  'Work Phone': 'work_phone',
  'Department Phone': 'department_phone',
  'Home Phone': 'home_phone',
  'Cell Phone': 'cell_phone',
  'Office Building': 'office_building',
  'Office Number': 'office_number',
  'Street 1': 'street_1',
  'Street 2': 'street_2',
  'City': 'city',
  'Province/State': 'province_state',
  'Postal/Zip Code': 'postal_zip_code',
  'Fax Number': 'fax_number',
  'Pager': 'pager',
  
  // Personal Information
  'Gender at Birth': 'gender_at_birth',
  'Declared Gender': 'declared_gender',
  'Country of Origin': 'country_of_origin',
  'Race/Ethnicity': 'race_ethnicity',
  'Languages': 'languages',
  'URL': 'url',
  
  // Emergency Contact
  'Emergency Contact': 'emergency_contact',
  'Emergency Contact Phone': 'emergency_contact_phone',
  'Country of Contact': 'country_of_contact',
  
  // Personal Address
  'Personal Street 1': 'personal_street_1',
  'Personal Street 2': 'personal_street_2',
  'Personal City': 'personal_city',
  'Personal Province/State': 'personal_province_state',
  'Personal Postal/Zip': 'personal_postal_zip',
};

const FACULTY_COLUMNS = [
  { value: 'intelicampus_id', label: 'Intelicampus ID' },
  { value: 'intelicampus_id2', label: 'Intelicampus ID 2' },
  { value: 'employee_id', label: 'Employee ID' },
  { value: 'sso_id', label: 'SSO ID' },
  { value: 'allow_login', label: 'Allow Login' },
  { value: 'faculty_unique_id', label: 'Faculty Unique ID' },
  { value: 'login_name', label: 'Login Name' },
  { value: 'first_name', label: 'First Name' },
  { value: 'preferred_name', label: 'Preferred Name' },
  { value: 'last_name', label: 'Last Name' },
  { value: 'middle_initial', label: 'Middle Initial' },
  { value: 'suffix', label: 'Suffix' },
  { value: 'work_email', label: 'Work Email' },
  { value: 'honorific', label: 'Honorific' },
  { value: 'primary_unit_id', label: 'Primary Unit ID' },
  { value: 'primary_unit_percentage', label: 'Primary Unit Percentage' },
  { value: 'primary_unit_sub_group', label: 'Primary Unit Sub Group' },
  { value: 'employment_status', label: 'Employment Status' },
  { value: 'date_started', label: 'Date Started' },
  { value: 'rank_name', label: 'Rank Name' },
  { value: 'tenure_status', label: 'Tenure Status' },
  { value: 'gender_at_birth', label: 'Gender at Birth' },
  { value: 'declared_gender', label: 'Declared Gender' },
  { value: 'country_of_origin', label: 'Country of Origin' },
  { value: 'race_ethnicity', label: 'Race/Ethnicity' },
  { value: 'languages', label: 'Languages' },
  { value: 'url', label: 'URL' },
  { value: 'office_building', label: 'Office Building' },
  { value: 'office_number', label: 'Office Number' },
  { value: 'work_phone', label: 'Work Phone' },
  { value: 'department_phone', label: 'Department Phone' },
  { value: 'home_phone', label: 'Home Phone' },
  { value: 'cell_phone', label: 'Cell Phone' },
  { value: 'street_1', label: 'Street 1' },
  { value: 'street_2', label: 'Street 2' },
  { value: 'city', label: 'City' },
  { value: 'province_state', label: 'Province/State' },
  { value: 'postal_zip_code', label: 'Postal/Zip Code' },
  { value: 'fax_number', label: 'Fax Number' },
  { value: 'pager', label: 'Pager' },
  { value: 'personal_city', label: 'Personal City' },
  { value: 'personal_province_state', label: 'Personal Province/State' },
  { value: 'personal_street_1', label: 'Personal Street 1' },
  { value: 'personal_street_2', label: 'Personal Street 2' },
  { value: 'personal_postal_zip', label: 'Personal Postal/Zip' },
  { value: 'personal_email', label: 'Personal Email' },
  { value: 'emergency_contact', label: 'Emergency Contact' },
  { value: 'emergency_contact_phone', label: 'Emergency Contact Phone' },
  { value: 'country_of_contact', label: 'Country of Contact' },
];

export default function ImportForm() {
  const [file, setFile] = useState<File | null>(null);
  const [excelData, setExcelData] = useState<ExcelData | null>(null);
  const [columnMapping, setColumnMapping] = useState<ColumnMapping>({});
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile) {
      setFile(selectedFile);
      setError(null);
      setSuccess(null);
      
      try {
        const reader = new FileReader();
        reader.onload = (e) => {
          try {
            const data = new Uint8Array(e.target?.result as ArrayBuffer);
            const workbook = XLSX.read(data, { type: 'array' });
            const worksheet = workbook.Sheets[workbook.SheetNames[0]];
            
            // Get the headers first
            const headers = XLSX.utils.sheet_to_json(worksheet, { header: 1 })[0] as string[];
            
            // Read the data with proper date parsing
            const jsonData = XLSX.utils.sheet_to_json(worksheet, { 
              raw: false,
              dateNF: 'yyyy/mm/dd'
            });
            
            if (jsonData.length > 0) {
              // Get the first row data with proper date formatting
              const firstRow = headers.reduce((obj, header) => {
                let value = (jsonData[0] as any)[header];
                
                // Check if the header is related to dates
                if (header.toLowerCase().includes('date') && value) {
                  // Ensure the date is in yyyy/mm/dd format
                  try {
                    const date = new Date(value);
                    if (!isNaN(date.getTime())) {
                      value = date.toISOString().split('T')[0].replace(/-/g, '/');
                    }
                  } catch (err) {
                    console.error('Date parsing error:', err);
                  }
                }
                
                obj[header] = value || '';
                return obj;
              }, {} as { [key: string]: any });
              
              setExcelData({ headers, firstRow });
              
              // Pre-populate column mappings based on common names
              const initialMapping: ColumnMapping = {};
              headers.forEach(header => {
                const normalizedHeader = header.trim();
                if (COLUMN_MAPPINGS[normalizedHeader]) {
                  initialMapping[header] = COLUMN_MAPPINGS[normalizedHeader];
                }
              });
              setColumnMapping(initialMapping);
            } else {
              setError('No data found in the Excel file');
            }
          } catch (err) {
            setError('Error reading Excel file. Please make sure it is a valid Excel file.');
            console.error('Excel reading error:', err);
          }
        };
        reader.readAsArrayBuffer(selectedFile);
      } catch (err) {
        setError('Error reading file. Please try again.');
        console.error('File reading error:', err);
      }
    }
  };

  const handleMappingChange = (excelColumn: string, facultyColumn: string) => {
    setColumnMapping(prev => ({
      ...prev,
      [excelColumn]: facultyColumn
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!file) {
      setError('Please select a file to import');
      return;
    }

    setIsUploading(true);
    setError(null);
    setSuccess(null);

    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('mapping', JSON.stringify(columnMapping));

      const response = await fetch('/api/admin/import', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Import failed');
      }

      setSuccess('Data imported successfully');
      setFile(null);
      setExcelData(null);
      setColumnMapping({});
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to import data. Please try again.');
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className="rounded-md bg-gray-50 p-4">
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label htmlFor="file-upload" className="block text-sm font-medium text-gray-700">
            Upload Excel/CSV File
          </label>
          <div className="mt-1 flex items-center">
            <input
              id="file-upload"
              type="file"
              accept=".xlsx,.xls,.csv"
              onChange={handleFileChange}
              className="block w-full text-sm text-gray-500
                file:mr-4 file:py-2 file:px-4
                file:rounded-md file:border-0
                file:text-sm file:font-semibold
                file:bg-blue-50 file:text-blue-700
                hover:file:bg-blue-100"
            />
          </div>
        </div>

        {excelData && (
          <div className="mt-4">
            <h3 className="text-lg font-medium text-gray-900">Map Columns</h3>
            <div className="mt-2">
              {/* Column Headers */}
              <div className="flex items-center space-x-4 mb-2 font-medium text-sm text-gray-700">
                <span className="w-1/4">Source Column</span>
                <span className="w-1/4">Source Data</span>
                <span className="w-1/2">Target Column</span>
              </div>
              
              {/* Column Mappings */}
              <div className="space-y-2">
                {excelData.headers.map((excelColumn) => (
                  <div key={excelColumn} className="flex items-center space-x-4">
                    <span className="text-sm text-gray-700 w-1/4">{excelColumn}</span>
                    <span className="text-sm text-gray-500 w-1/4">
                      {excelData.firstRow[excelColumn] || ''}
                    </span>
                    <select
                      value={columnMapping[excelColumn] || ''}
                      onChange={(e) => handleMappingChange(excelColumn, e.target.value)}
                      className="block w-1/2 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                    >
                      <option value="">Select a column</option>
                      {FACULTY_COLUMNS.map((col) => (
                        <option key={col.value} value={col.value}>
                          {col.label}
                        </option>
                      ))}
                    </select>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {error && (
          <div className="rounded-md bg-red-50 p-4">
            <div className="flex">
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">{error}</h3>
              </div>
            </div>
          </div>
        )}

        {success && (
          <div className="rounded-md bg-green-50 p-4">
            <div className="flex">
              <div className="ml-3">
                <h3 className="text-sm font-medium text-green-800">{success}</h3>
              </div>
            </div>
          </div>
        )}

        <div className="mt-4">
          <button
            type="submit"
            disabled={!file || isUploading}
            className="inline-flex items-center rounded-md bg-blue-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <ArrowUpTrayIcon className="mr-2 h-5 w-5" />
            {isUploading ? 'Importing...' : 'Import Data'}
          </button>
        </div>
      </form>
    </div>
  );
} 