'use client';
import { useForm, useFieldArray } from "react-hook-form";
import { useRouter } from "next/navigation";
import { useState } from "react";

type SupervisorEntry = {
  student_name: string;
  supervisor: string;
};

type FormValues = {
  first_name:string;
  last_name:string;
  department: string;
  dept_name: string;
  applicant_name: string;
  date_from: string;
  date_to: string;
  student_name: string;
  no_grad_student: string;
  delegated_supervisor: SupervisorEntry[];
};

export default function Form({ initialData = {} }: { initialData?: Partial<FormValues> }) {
  const router = useRouter();
  const [error, setError] = useState("");

  const { register, control, handleSubmit } = useForm<FormValues>({
    defaultValues: {
      ...initialData,
      delegated_supervisor: [{ student_name: "", supervisor: "" }],
      
    },
  });

  const { fields: delegated_supervisor,
    append: appenddelegated_supervisor,
    remove: removedelegated_supervisor, } = useFieldArray({
    control,
    name: "delegated_supervisor",
  });

  const onSubmit = async (data: FormValues) => {

      try {
        const response = await fetch('/api/sabbaticalForm/sabbatical-step3', {
          method: 'POST',
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(data),
        });
  
        const res = await response.json();
  
        if (res.success) {
          router.push('/dashboard/sabbatical/sabbatical-step4?message=Request+submitted+Complete+Step+4&status=success');
        } else {
          setError(res.error || 'Submission failed.');
        }
      } catch (err) {
        setError('Unexpected error occurred.');
      }
  };

  return (
    <div className='w-full max-w-full px-5 m-auto lg:w-11/12 '>
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6 relative mb-32">
        {error && <p className="text-red-500">{error}</p>}
      
        <div  className=" top-0 left-0 flex flex-col w-full min-w-0 p-4 break-words bg-white border-0   shadow-soft-xl rounded-2xl bg-clip-border h-auto opacity-100 visible">

            <div>
              <div className="flex flex-wrap mt-4 -mx-3">
                <div className="w-full max-w-full px-3 flex-0 sm:w-4/12">
                  <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="dept_name">Department</label>
                  <input type="text" id="dept_name" name="dept_name" defaultValue={initialData.department} placeholder="eg. Michael Brar" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                </div>
                <div className="w-full max-w-full px-3 mt-4 flex-0 sm:mt-0 sm:w-4/12">
                  <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="applicant_name">Applicant Name</label>
                  <input type="text" id="applicant_name" name="applicant_name" defaultValue={initialData.applicant_name} placeholder="eg. DMS890" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                </div>
              </div>

              <div className="flex flex-wrap -mx-3 p-4 w-full ">
                <p className='leading-normal text-sm w-full font-bold'>
                  During my proposed leave of absence
                </p>
              </div>
              <div className="flex flex-wrap -mx-3">
                <div className="w-full max-w-full px-3 flex-0 sm:w-4/12">
                  <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="date_from">From Date</label>
                  <input type="date" id="date_from" {...register("date_from", { required: true })} name="date_from" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                </div>
                <div className="w-full max-w-full px-3 mt-4 flex-0 sm:mt-0 sm:w-4/12">
                  <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="date_to">To Date</label>
                  <input type="date" id="date_to" {...register("date_to", { required: true })}  className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                </div>
              </div>
              <div className="flex flex-wrap -mx-3 p-4 w-full font-bold">
                <p className='leading-normal text-sm '>I have made the following arrangements for my graduate students: </p> <button type="button" onClick={() => appenddelegated_supervisor({ student_name: "", supervisor: "" })} className='text-blue-700'>  + Add Entry
                </button>
              </div>

              {delegated_supervisor.map((field, index) => (
                                <div key={field.id} className="flex flex-wrap -mx-3">
                                <div className="w-full max-w-full px-3 flex-0 sm:w-4/12">
                                  <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="inst_name">Student’s Name</label>
                                  <input type="text" id="student_name" {...register(`delegated_supervisor.${index}.student_name`)} placeholder="eg. Michael Neil" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                                </div>
                                <div className="w-full max-w-full px-3 mt-4 flex-0 sm:mt-0 sm:w-4/12">
                                  <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="lab_room_number">Delegated Supervisor</label>
                                  <input type="text" id="supervisor" {...register(`delegated_supervisor.${index}.supervisor`)} placeholder="eg. Brian Warner" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                                </div>
                                <div className="p-4 rounded  sm:w-4/12">
                                      <button type="button" onClick={() => removedelegated_supervisor(index)} className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block  appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding mt-2 mr-2 px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none">X</button>
                                </div>
                            </div>

                ))}



              <div className="flex flex-wrap -mx-3 mt-4 p-4 w-full ">
                  <h5 className="md:mt-3 font-bold  z-10 mb-1 text-transparent bg-clip-text bg-gradient-to-tl from-purple-700 to-pink-500" >OR: Declaration of no graduate students during leave:</h5>
              </div>

              <div className="flex flex-wrap -mx-3">
                <div className="w-full max-w-full px-3 flex-0 sm:w-8/12">
                  <input type="checkbox" id="no_grad_student" value="true" name="no_grad_student" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft  appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                  <label htmlFor='no_grad_student' className="mb-2 ml-1 font-bold text-xs text-slate-700 /80"> I do not have and will not have any graduate students to supervise during my leave.</label>     
                </div>
              </div>

              <div className="flex justify-end mt-6">
                <button type="submit" aria-controls="address" next-form-btn=""  className="inline-block px-6 py-3 m-0 ml-2 text-xs font-bold text-center text-white uppercase align-middle transition-all border-0 rounded-lg cursor-pointer ease-soft-in leading-pro tracking-tight-soft bg-gradient-to-tl from-purple-700 to-pink-500 shadow-soft-md bg-150 bg-x-25 hover:scale-102 active:opacity-85">Save and Continue</button>
              </div>

            </div>
        </div>
        
      </form>
    </div>
  );
}
        
      
