"use client";

import { useEffect, useState } from "react";
import { ChevronDown, ChevronRight } from "lucide-react";

interface Committee {
  committee_id: number;
  name: string;
  short_name: string;
  primary_unit_id: number;
  effective_date: string;
  previous_name: string | null;
  description: string | null;
  is_deleted: boolean;
}

interface Unit {
  unit_id: number;
  full_name: string;
  committees: Committee[];
}

export default function CommitteeHierarchy() {
  const [units, setUnits] = useState<Unit[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [expandedUnits, setExpandedUnits] = useState<Set<number>>(new Set());

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await fetch('/api/admin/committees');
        if (!response.ok) {
          throw new Error('Failed to fetch committee data');
        }
        const committees: Committee[] = await response.json();

        // Group committees by primary unit
        const unitMap = new Map<number, Unit>();
        committees.forEach(committee => {
          if (!unitMap.has(committee.primary_unit_id)) {
            unitMap.set(committee.primary_unit_id, {
              unit_id: committee.primary_unit_id,
              full_name: `Unit ${committee.primary_unit_id}`, // We'll need to fetch unit names
              committees: []
            });
          }
          unitMap.get(committee.primary_unit_id)!.committees.push(committee);
        });

        setUnits(Array.from(unitMap.values()));
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const toggleUnit = (unitId: number) => {
    setExpandedUnits(prev => {
      const next = new Set(prev);
      if (next.has(unitId)) {
        next.delete(unitId);
      } else {
        next.add(unitId);
      }
      return next;
    });
  };

  if (loading) return <div>Loading data...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div className="rounded-md border p-4">
      {units.map(unit => {
        const isExpanded = expandedUnits.has(unit.unit_id);
        return (
          <div key={unit.unit_id}>
            <div
              className="flex items-center gap-2 py-2 cursor-pointer hover:bg-gray-100"
              onClick={() => toggleUnit(unit.unit_id)}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault();
                  toggleUnit(unit.unit_id);
                }
              }}
              tabIndex={0}
              role="button"
              aria-label={`Toggle ${unit.full_name} committee`}
            >
              {isExpanded ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
              <span className="font-medium">{unit.full_name}</span>
              <span className="text-sm text-gray-500">({unit.committees.length} committees)</span>
            </div>
            {isExpanded && (
              <div className="ml-6 space-y-2">
                {unit.committees.map(committee => (
                  <div key={committee.committee_id} className="py-1">
                    <div className="font-medium">{committee.name} ({committee.short_name})</div>
                    <div className="text-sm text-gray-500">
                      Effective: {new Date(committee.effective_date).toLocaleDateString()}
                      {committee.previous_name && ` | Previous name: ${committee.previous_name}`}
                    </div>
                    {committee.description && (
                      <div className="text-sm mt-1">{committee.description}</div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
} 