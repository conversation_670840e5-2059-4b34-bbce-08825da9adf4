"use client";

import { useEffect, useState } from "react";
import { ChevronRight, ChevronDown } from "lucide-react";

interface Unit {
  unit_id: number;
  full_name: string;
  level_number: number;
  parent_unit_id: number | null;
  children?: Unit[];
}

export function UnitHierarchy() {
  const [units, setUnits] = useState<Unit[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [expandedUnits, setExpandedUnits] = useState<Set<number>>(new Set());

  useEffect(() => {
    async function fetchUnits() {
      try {
        const response = await fetch('/api/admin/units');
        if (!response.ok) {
          throw new Error('Failed to fetch units');
        }
        const flatUnits = await response.json();
        
        // Convert flat list to hierarchical structure
        const unitMap = new Map<number, Unit>();
        const rootUnits: Unit[] = [];

        // First pass: create unit objects
        flatUnits.forEach((unit: Unit) => {
          unitMap.set(unit.unit_id, { ...unit, children: [] });
        });

        // Second pass: build hierarchy
        flatUnits.forEach((unit: Unit) => {
          const unitWithChildren = unitMap.get(unit.unit_id)!;
          if (unit.parent_unit_id) {
            const parent = unitMap.get(unit.parent_unit_id);
            if (parent) {
              parent.children = parent.children || [];
              parent.children.push(unitWithChildren);
            }
          } else {
            rootUnits.push(unitWithChildren);
          }
        });

        setUnits(rootUnits);
      } catch (err) {
        setError(err instanceof Error ? err.message : "Failed to fetch units");
      } finally {
        setLoading(false);
      }
    }

    fetchUnits();
  }, []);

  const toggleUnit = (unitId: number) => {
    setExpandedUnits(prev => {
      const next = new Set(prev);
      if (next.has(unitId)) {
        next.delete(unitId);
      } else {
        next.add(unitId);
      }
      return next;
    });
  };

  const renderUnit = (unit: Unit, level: number = 0) => {
    const hasChildren = unit.children && unit.children.length > 0;
    const isExpanded = expandedUnits.has(unit.unit_id);

    return (
      <div key={unit.unit_id}>
        <div
          className="flex items-center gap-2 py-1 hover:bg-gray-50 cursor-pointer"
          style={{ paddingLeft: `${level * 20}px` }}
          onClick={() => hasChildren && toggleUnit(unit.unit_id)}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              hasChildren && toggleUnit(unit.unit_id);
            }
          }}
          tabIndex={0}
          role="button"
          aria-label={`Toggle ${unit.full_name} unit`}
        >
          <span className="w-4 h-4 flex items-center justify-center">
            {hasChildren ? (
              isExpanded ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />
            ) : (
              <span className="text-gray-400">•</span>
            )}
          </span>
          <span className="font-medium">{unit.full_name}</span>
          <span className="text-sm text-gray-500">(ID: {unit.unit_id}, Level: {unit.level_number})</span>
        </div>
        {hasChildren && isExpanded && (
          <div className="ml-4">
            {unit.children!.map(child => renderUnit(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  if (loading) {
    return <div>Loading...</div>;
  }

  if (error) {
    return <div className="text-red-500">Error: {error}</div>;
  }

  return (
    <div className="rounded-md border p-4">
      {units.map(unit => renderUnit(unit))}
    </div>
  );
} 