"use client";

import { useEffect, useState } from "react";
import { ChevronDown, ChevronRight } from "lucide-react";
import SearchBar from "./SearchBar";

interface Faculty {
  intelicampus_id: string;
  sso_id: string;
  first_name: string | null;
  last_name: string | null;
  work_email: string | null;
  primary_unit_id: number;
  unit_name?: string;
  primary_unit_percentage: number;
  tenure_status: string;
  position_id: number;
  job_family?: string;
}

interface Unit {
  unit_id: number;
  full_name: string;
  level_number: number;
  parent_unit_id: number | null;
  children?: Unit[];
  faculty?: Faculty[];
}

export default function FacultyHierarchy() {
  const [units, setUnits] = useState<Unit[]>([]);
  const [allFaculty, setAllFaculty] = useState<Faculty[]>([]);
  const [filteredFaculty, setFilteredFaculty] = useState<Faculty[]>([]);
  // Initialize with top-level units expanded
  const [expandedUnits, setExpandedUnits] = useState<Set<number>>(new Set());
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");

  useEffect(() => {
    async function fetchData() {
      try {
        const [unitsResponse, facultyResponse] = await Promise.all([
          fetch('/api/admin/units'),
          fetch('/api/sys_admin/faculty')
        ]);

        if (!unitsResponse.ok || !facultyResponse.ok) {
          throw new Error('Failed to fetch data');
        }

        const flatUnits = await unitsResponse.json();
        const facultyData = await facultyResponse.json();

        setAllFaculty(facultyData);
        setFilteredFaculty(facultyData);

        // Convert flat list to hierarchical structure
        const unitMap = new Map<number, Unit>();
        const rootUnits: Unit[] = [];

        // First pass: create unit objects
        flatUnits.forEach((unit: Unit) => {
          unitMap.set(unit.unit_id, { ...unit, children: [], faculty: [] });
        });

        // Second pass: build hierarchy
        flatUnits.forEach((unit: Unit) => {
          const unitWithChildren = unitMap.get(unit.unit_id)!;
          if (unit.parent_unit_id) {
            const parent = unitMap.get(unit.parent_unit_id);
            if (parent) {
              parent.children = parent.children || [];
              parent.children.push(unitWithChildren);
            }
          } else {
            rootUnits.push(unitWithChildren);
          }
        });

        // Third pass: assign faculty to units
        facultyData.forEach((faculty: Faculty) => {
          const unit = unitMap.get(faculty.primary_unit_id);
          if (unit) {
            unit.faculty = unit.faculty || [];
            unit.faculty.push(faculty);
          }
        });

        // Expand top-level units by default
        const initialExpandedUnits = new Set<number>();
        rootUnits.forEach(unit => {
          if (unit.faculty && unit.faculty.length > 0) {
            initialExpandedUnits.add(unit.unit_id);
          }
        });

        setExpandedUnits(initialExpandedUnits);
        setUnits(rootUnits);
      } catch (err) {
        setError(err instanceof Error ? err.message : "Failed to fetch data");
      } finally {
        setLoading(false);
      }
    }

    fetchData();
  }, []);

  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredFaculty(allFaculty);
      return;
    }

    const query = searchQuery.toLowerCase();
    const filtered = allFaculty.filter(faculty =>
      (faculty.first_name?.toLowerCase() || '').includes(query) ||
      (faculty.last_name?.toLowerCase() || '').includes(query) ||
      (faculty.work_email?.toLowerCase() || '').includes(query) ||
      (faculty.unit_name?.toLowerCase() || '').includes(query) ||
      (faculty.job_family?.toLowerCase() || '').includes(query) ||
      (faculty.tenure_status?.toLowerCase() || '').includes(query)
    );

    setFilteredFaculty(filtered);
  }, [searchQuery, allFaculty]);

  const toggleUnit = (unitId: number) => {
    setExpandedUnits(prev => {
      const next = new Set(prev);
      if (next.has(unitId)) {
        next.delete(unitId);
      } else {
        next.add(unitId);
      }
      return next;
    });
  };

  const renderUnit = (unit: Unit, level: number = 0) => {
    const hasChildren = unit.children && unit.children.length > 0;
    const isExpanded = expandedUnits.has(unit.unit_id);
    const unitFaculty = unit.faculty || [];

    // Filter faculty for this unit based on search query
    const visibleFaculty = searchQuery.trim()
      ? unitFaculty.filter(f => filteredFaculty.some(ff => ff.intelicampus_id === f.intelicampus_id))
      : unitFaculty;

    // Skip rendering this unit if it has no faculty matching the search and no children
    if (searchQuery.trim() && visibleFaculty.length === 0 && !hasChildren) {
      return null;
    }

    // For units with children, check if any children or their descendants have matching faculty
    if (searchQuery.trim() && visibleFaculty.length === 0 && hasChildren) {
      // Helper function to check if any descendant has matching faculty
      const hasMatchingFaculty = (units: Unit[]): boolean => {
        for (const unit of units) {
          const unitFaculty = unit.faculty || [];
          const hasMatching = unitFaculty.some(f =>
            filteredFaculty.some(ff => ff.intelicampus_id === f.intelicampus_id)
          );

          if (hasMatching) return true;

          if (unit.children && unit.children.length > 0) {
            if (hasMatchingFaculty(unit.children)) return true;
          }
        }
        return false;
      };

      // Skip this unit if none of its descendants have matching faculty
      if (!hasMatchingFaculty(unit.children || [])) {
        return null;
      }
    }

    return (
      <div key={unit.unit_id}>
        <div
          className="flex items-center gap-2 py-1 hover:bg-gray-50 cursor-pointer"
          style={{ paddingLeft: `${level * 20}px` }}
          onClick={() => toggleUnit(unit.unit_id)}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              toggleUnit(unit.unit_id);
            }
          }}
          tabIndex={0}
          role="button"
          aria-label={`Toggle ${unit.unit_name} faculty`}
        >
          <span className="w-4 h-4 flex items-center justify-center">
            {isExpanded ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
          </span>
          <span className="font-medium">{unit.full_name}</span>
          <span className="text-sm text-gray-500">
            (ID: {unit.unit_id}, Level: {unit.level_number}, Faculty: {visibleFaculty.length})
          </span>
        </div>

        {isExpanded && (
          <div className="ml-4">
            {/* Display faculty for this unit */}
            {visibleFaculty.length > 0 && (
              <div className="border-l-2 border-gray-200 pl-4 ml-2 mt-1 mb-2">
                {visibleFaculty.map(faculty => (
                  <div
                    key={faculty.intelicampus_id}
                    className="py-1 text-sm flex items-start"
                  >
                    <span className="text-gray-400 mr-2">•</span>
                    <div>
                      <div className="font-medium">
                        {`${faculty.first_name || ''} ${faculty.last_name || ''}`}
                      </div>
                      <div className="text-xs text-gray-500">
                        {faculty.work_email} | {faculty.tenure_status} | {faculty.job_family || 'N/A'}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Render child units */}
            {hasChildren && unit.children!.map(child => renderUnit(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  if (loading) {
    return <div>Loading...</div>;
  }

  if (error) {
    return <div className="text-red-500">Error: {error}</div>;
  }

  return (
    <div className="space-y-4">
      <div className="max-w-md">
        <SearchBar
          onSearch={setSearchQuery}
          placeholder="Search by name, email, unit, or job family..."
        />
      </div>
      <div className="rounded-md border p-4">
        {units.map(unit => renderUnit(unit))}
      </div>
      <div className="text-sm text-gray-500">
        Showing {filteredFaculty.length} of {allFaculty.length} faculty members
      </div>
    </div>
  );
}