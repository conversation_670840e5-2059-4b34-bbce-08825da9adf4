"use client";

import { useSession } from "next-auth/react";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState, useMemo } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardT<PERSON>le, CardFooter } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  CalendarIcon,
  ClipboardCheckIcon,
  ClipboardListIcon,
  PencilIcon,
  EyeIcon,
  CheckCircle2,
  Clock,
  FileText
} from "lucide-react";
import Link from "next/link";
import { toast } from "sonner";
import { Badge } from "@/components/ui/badge";
import SubmissionTab from "./components/SubmissionTab";
import CommitteeManagement from "./components/CommitteeManagement";



interface Workflow {
  id: number;
  unit_id: number;
  unit_name: string;
  start_dt: string;
  end_dt: string;
  status: string;
  description?: string;
  created_at: string;
  updated_at: string;
  created_by: string;
  level_number: number;
  can_edit: boolean;
}

export default function MeritReviewPage() {
  const { data: session, status } = useSession();
  const router = useRouter();

  const searchParams = useSearchParams();
  const [activeTab, setActiveTab] = useState(searchParams.get("tab") || "overview");
  const [workflows, setWorkflows] = useState<any[]>([]);
  const [activeWorkflows, setActiveWorkflows] = useState<any[]>([]);
  const [isLoadingActiveWorkflows, setIsLoadingActiveWorkflows] = useState(false);
  const [isLoadingWorkflows, setIsLoadingWorkflows] = useState(false);
  const [userUnitId, setUserUnitId] = useState(0);

  // Get user roles with useMemo to prevent dependency changes
  const roles = useMemo(() => session?.user?.roles || [], [session?.user?.roles]);

  // Determine which view to show based on user role
  const isFacultyAdmin = roles.includes("faculty_admin");
  const isDepartmentAdmin = roles.includes("department_admin");
  const isRegularUser = roles.includes("regular_user");
  const isSystemAdmin = roles.includes("system_admin");

  // Update URL when tab changes
  useEffect(() => {
    const params = new URLSearchParams(searchParams);
    params.set("tab", activeTab);
    router.push(`/dashboard/merit-review?${params.toString()}`, { scroll: false });
  }, [activeTab, router, searchParams]);

  // Redirect if not authenticated
  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/login");
    }
  }, [status, router]);

  // Fetch active workflows for the overview tab
  useEffect(() => {
    const fetchActiveWorkflows = async () => {
      if (status === "authenticated") {
        setIsLoadingActiveWorkflows(true);
        try {
          const response = await fetch("/api/merit-review/workflow/active");
          if (response.ok) {
            const data = await response.json();
            if (data.active) {
              setActiveWorkflows([data.active]);
            } else {
              setActiveWorkflows([]);
            }
          } else {
            console.error("Failed to fetch active workflows");
          }
        } catch (error) {
          console.error("Error fetching active workflows:", error);
        } finally {
          setIsLoadingActiveWorkflows(false);
        }
      }
    };

    fetchActiveWorkflows();
  }, [status]);

  useEffect(() => {
    // Only fetch user unit information if the user has roles that need it
    if (status === "authenticated" &&
        (roles.includes("faculty_admin") ||
         roles.includes("department_admin") ||
         roles.includes("system_admin"))) {
      const fetchUserUnit = async () => {
        try {
          const response = await fetch("/api/faculty/current");
          if (!response.ok) {
            throw new Error("Failed to fetch faculty data");
          }
          const data = await response.json();
          setUserUnitId(data.primary_unit_id);
        } catch (error) {
          console.error("Error fetching user unit:", error);
          toast.error("Failed to fetch user unit");
        }
      };
      fetchUserUnit();
    }
  }, [status, roles]);

  if (status === "loading") {
    return <div>Loading...</div>;
  }

  // Fetch workflows function with proper dependencies
  const fetchWorkflows = useMemo(() => async () => {
    try {
      const response = await fetch(
        `/api/merit-review/workflow?user_unit_id=${userUnitId}`
      );
      if (!response.ok) {
        throw new Error("Failed to fetch workflows");
      }
      const data = await response.json();
      setWorkflows(data);
    } catch (error) {
      console.error("Error fetching workflows:", error);
      toast.error("Failed to fetch workflows");
    }
  }, [userUnitId]);

  const canEditWorkflow = (workflow: Workflow) => {
    if (!session?.user?.roles) return false;

    // System admin can edit any workflow
    if (session.user.roles.includes("system_admin")) return true;

    // Faculty admin can edit faculty level workflows (level 3)
    if (session.user.roles.includes("faculty_admin") && workflow.level_number === 3) return true;

    // Department admin can edit their own department's workflows (level 4)
    if (session.user.roles.includes("department_admin") && workflow.level_number === 4) {
      return workflow.can_edit;
    }

    return false;
  };

  const handleDeleteWorkflow = async (workflowId: number) => {
    if (!confirm("Are you sure you want to delete this workflow? This action cannot be undone.")) {
      return;
    }

    try {
      const response = await fetch(`/api/merit-review/workflow/${workflowId}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to delete workflow");
      }

      toast.success("Workflow deleted successfully");
      // Refresh the workflows list
      if (roles.includes("faculty_admin") ||
          roles.includes("department_admin") ||
          roles.includes("system_admin")) {
        const facultyResponse = await fetch("/api/faculty/current");
        if (!facultyResponse.ok) {
          throw new Error("Failed to fetch faculty data");
        }
        const facultyData = await facultyResponse.json();

        const workflowsResponse = await fetch(`/api/merit-review/workflow?user_unit_id=${facultyData.primary_unit_id}`);
        if (workflowsResponse.ok) {
          const data = await workflowsResponse.json();
          setWorkflows(data);
        }
      }
    } catch (error) {
      console.error("Error deleting workflow:", error);
      toast.error(error instanceof Error ? error.message : "Failed to delete workflow");
    }
  };

  // Fetch workflows when the configuration tab is active and user has proper roles
  useEffect(() => {
    if (status === "authenticated" &&
        (isFacultyAdmin || isDepartmentAdmin || isSystemAdmin) &&
        activeTab === "configuration" &&
        userUnitId > 0) {
      setIsLoadingWorkflows(true);
      fetchWorkflows().finally(() => setIsLoadingWorkflows(false));
    }
  }, [status, activeTab, isFacultyAdmin, isDepartmentAdmin, isSystemAdmin, userUnitId, fetchWorkflows]);

  return (
    <div className="container mx-auto py-6">


      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3 lg:grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          {(isFacultyAdmin || isDepartmentAdmin || isSystemAdmin) && (
            <TabsTrigger value="configuration">Configuration</TabsTrigger>
          )}
          {(isDepartmentAdmin || isSystemAdmin) && (
            <TabsTrigger value="committee">Committee</TabsTrigger>
          )}
          {(isRegularUser || isSystemAdmin || isFacultyAdmin || isDepartmentAdmin) && (
            <TabsTrigger value="submission">My Submission</TabsTrigger>
          )}
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Merit Review Process</CardTitle>
              <CardDescription>
                Overview of the merit review process and current status
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {isLoadingActiveWorkflows ? (
                <div className="text-center py-4">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
                  <p className="mt-2 text-sm text-muted-foreground">Loading active workflows...</p>
                </div>
              ) : activeWorkflows.length > 0 ? (
                <div className="space-y-4">
                  {activeWorkflows.map((workflow) => (
                    <Card key={workflow.id}>
                      <CardHeader>
                        <CardTitle className="text-lg">{workflow.unit_name}</CardTitle>
                        <CardDescription>
                          Status: {workflow.status} |
                          Period: {new Date(workflow.start_dt).toLocaleDateString()} - {new Date(workflow.end_dt).toLocaleDateString()}
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <Card>
                            <CardHeader className="pb-2">
                              <CardTitle className="text-sm font-medium">Timeline</CardTitle>
                            </CardHeader>
                            <CardContent>
                              <div className="flex items-center">
                                <CalendarIcon className="h-4 w-4 mr-2 text-muted-foreground" />
                                <p>
                                  {new Date(workflow.start_dt).toLocaleDateString()} - {new Date(workflow.end_dt).toLocaleDateString()}
                                </p>
                              </div>
                            </CardContent>
                          </Card>
                          <Card>
                            <CardHeader className="pb-2">
                              <CardTitle className="text-sm font-medium">Status</CardTitle>
                            </CardHeader>
                            <CardContent>
                              <div className="flex items-center">
                                <ClipboardListIcon className="h-4 w-4 mr-2 text-muted-foreground" />
                                <p>{workflow.status}</p>
                              </div>
                            </CardContent>
                          </Card>
                          <Card>
                            <CardHeader className="pb-2">
                              <CardTitle className="text-sm font-medium">Actions</CardTitle>
                            </CardHeader>
                            <CardContent>
                              <div className="flex items-center">
                                <ClipboardCheckIcon className="h-4 w-4 mr-2 text-muted-foreground" />
                                <p>
                                  {isRegularUser ? (
                                    <Link href="/dashboard/merit-review/submission/v2" className="text-primary hover:underline">
                                      Submit Report
                                    </Link>
                                  ) : (
                                    <Link href={`/dashboard/merit-review/configuration/view/${workflow.id}`} className="text-primary hover:underline">
                                      View Details
                                    </Link>
                                  )}
                                </p>
                              </div>
                            </CardContent>
                          </Card>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">Timeline</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center">
                        <CalendarIcon className="h-4 w-4 mr-2 text-muted-foreground" />
                        <p>No active workflow</p>
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">Status</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center">
                        <ClipboardListIcon className="h-4 w-4 mr-2 text-muted-foreground" />
                        <p>Not started</p>
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">Actions</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center">
                        <ClipboardCheckIcon className="h-4 w-4 mr-2 text-muted-foreground" />
                        <p>No actions available</p>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Configuration Tab - Only for faculty_admin */}
        {(isFacultyAdmin || isDepartmentAdmin || isSystemAdmin) && (
          <TabsContent value="configuration" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Create New Workflow</CardTitle>
                <CardDescription>
                  Set up the merit review process timeline and parameters
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <p>Create a new merit review workflow configuration.</p>
                    <Button asChild>
                      <Link href="/dashboard/merit-review/configuration">
                        Create New Workflow
                      </Link>
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Existing Workflow Configurations</CardTitle>
                <CardDescription>
                  List of all merit review workflow configurations
                </CardDescription>
              </CardHeader>
              <CardContent>

                {workflows.length === 0 ? (
                  <div className="text-center py-4">
                    <p className="mt-2 text-sm text-muted-foreground">No workflows found</p>

                  </div>
                ) : (
                  <div className="space-y-4">
                    {workflows.map((workflow) => (
                      <Card key={workflow.id}>
                        <CardHeader>
                          <CardTitle className="text-lg">{workflow.unit_name}</CardTitle>
                          <CardDescription>

                            Status: {workflow.status} |

                            Period: {new Date(workflow.start_dt).toLocaleDateString()} - {new Date(workflow.end_dt).toLocaleDateString()}
                          </CardDescription>
                        </CardHeader>
                        <CardContent>
                          <div className="flex justify-between items-center">
                            <p className="text-sm text-muted-foreground">
                              {workflow.description || "No description provided"}
                            </p>
                            <div className="flex gap-2">
                              <Button variant="outline" size="sm" asChild>

                                <Link href={`/dashboard/merit-review/configuration/edit/${workflow.id}`}>
                                  Edit
                                </Link>
                              </Button>
                              <Button variant="outline" size="sm" asChild>

                                <Link href={`/dashboard/merit-review/configuration/view/${workflow.id}`}>
                                  View Details
                                </Link>
                              </Button>

                              {workflow.status === "draft" && canEditWorkflow(workflow) && (
                                <>
                                  <Button variant="outline" size="sm" asChild>
                                    <Link href={`/dashboard/merit-review/configuration/edit/${workflow.id}`}>
                                      Edit
                                    </Link>
                                  </Button>
                                  <Button
                                    variant="destructive"
                                    size="sm"
                                    onClick={() => handleDeleteWorkflow(workflow.id)}
                                  >
                                    Delete
                                  </Button>
                                </>
                              )}

                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                    {workflows.length === 0 && (
                      <div className="text-center py-4 text-muted-foreground">
                        No workflow configurations found
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        )}

        {/* Committee Tab - Only for department_admin */}
        {(isDepartmentAdmin || isSystemAdmin) && (
          <TabsContent value="committee" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Merit Review Committee</CardTitle>
                <CardDescription>
                  Manage committee membership and conflicts of interest
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <CommitteeManagement />
              </CardContent>
            </Card>
          </TabsContent>
        )}

        {/* Submission Tab - For regular_user, faculty_admin, and department_admin */}
        {(isRegularUser || isSystemAdmin || isFacultyAdmin || isDepartmentAdmin) && (
          <TabsContent value="submission" className="space-y-4">
            <SubmissionTab />
          </TabsContent>
        )}
      </Tabs>
    </div>
  );
}
