"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { toast } from "sonner";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Loader2, FileText, CheckCircle2, AlertCircle, Clock, Star } from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";

interface Submission {
  id: number;
  faculty_id: number;
  faculty_name: string;
  unit_id: number;
  unit_name: string;
  report_doc: string;
  create_dt: string;
  update_dt: string;
  status: string;
  ratings: Rating[];
}

interface Rating {
  id: number;
  teaching_rating: number;
  research_rating: number;
  service_rating: number;
  comments: string;
  created_at: string;
}

export default function ReviewPage() {
  const { data: session, status: authStatus } = useSession();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [submissions, setSubmissions] = useState<Submission[]>([]);
  const [selectedSubmission, setSelectedSubmission] = useState<Submission | null>(null);
  const [ratings, setRatings] = useState({
    teaching: 0,
    research: 0,
    service: 0,
    comments: "",
  });

  // Redirect if not authenticated
  useEffect(() => {
    if (authStatus === "unauthenticated") {
      router.push("/login");
    }
  }, [authStatus, router]);

  // Fetch submissions
  const fetchSubmissions = async () => {
    try {
      const response = await fetch("/api/merit-review/review/submissions");
      if (!response.ok) {
        throw new Error("Failed to fetch submissions");
      }
      const data = await response.json();
      setSubmissions(data);
    } catch (error) {
      console.error("Error fetching submissions:", error);
      toast.error("Failed to fetch submissions");
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch initial data
  useEffect(() => {
    if (authStatus === "authenticated") {
      fetchSubmissions();
    }
  }, [authStatus]);

  const handleRatingChange = (category: string, value: string) => {
    setRatings(prev => ({
      ...prev,
      [category]: parseInt(value)
    }));
  };

  const handleCommentsChange = (value: string) => {
    setRatings(prev => ({
      ...prev,
      comments: value
    }));
  };

  const handleSubmitRating = async (submissionId: number, rating: number) => {
    try {
      const response = await fetch(`/api/merit-review/ratings/${submissionId}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ rating }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to submit rating");
      }

      toast.success("Rating submitted successfully");
      // Refresh submissions to get updated ratings
      fetchSubmissions();
    } catch (error) {
      console.error("Error submitting rating:", error);
      toast.error(error instanceof Error ? error.message : "Failed to submit rating");
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "submitted":
        return <CheckCircle2 className="h-5 w-5 text-green-500" />;
      case "in_progress":
        return <Clock className="h-5 w-5 text-yellow-500" />;
      case "needs_revision":
        return <AlertCircle className="h-5 w-5 text-red-500" />;
      default:
        return <FileText className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "submitted":
        return "Submitted";
      case "in_progress":
        return "In Progress";
      case "needs_revision":
        return "Needs Revision";
      default:
        return "Not Started";
    }
  };

  if (authStatus === "loading") {
    return <div>Loading...</div>;
  }

  return (
    <div className="container mx-auto py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Review Submissions</h1>
        <Button onClick={() => router.push("/dashboard/merit-review")}>
          Back to Merit Review
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
            <CardTitle>Submissions for Review</CardTitle>
                  <CardDescription>
              Review and rate faculty submissions
                  </CardDescription>
                </CardHeader>
                <CardContent>
            {isLoading ? (
              <div className="flex justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin" />
                    </div>
                  ) : (
                    <Table>
                      <TableHeader>
                        <TableRow>
                    <TableHead>Faculty</TableHead>
                    <TableHead>Unit</TableHead>
                          <TableHead>Status</TableHead>
                    <TableHead>Ratings</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                  {submissions.map((submission) => (
                    <TableRow key={submission.id}>
                      <TableCell>{submission.faculty_name}</TableCell>
                      <TableCell>{submission.unit_name}</TableCell>
                            <TableCell>
                        <div className="flex items-center gap-2">
                          {getStatusIcon(submission.status)}
                          {getStatusText(submission.status)}
                        </div>
                            </TableCell>
                            <TableCell>
                        {submission.ratings.length} / 3
                      </TableCell>
                      <TableCell className="text-right">
                              <Button
                                variant="outline"
                                size="sm"
                          onClick={() => setSelectedSubmission(submission)}
                              >
                                Review
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))}
                  {submissions.length === 0 && (
                    <TableRow>
                      <TableCell colSpan={5} className="text-center py-8">
                        No submissions found
                      </TableCell>
                    </TableRow>
                  )}
                      </TableBody>
                    </Table>
                  )}
                </CardContent>
              </Card>

        {selectedSubmission && (
              <Card>
                <CardHeader>
              <CardTitle>Submit Rating</CardTitle>
                  <CardDescription>
                Rate {selectedSubmission.faculty_name}&apos;s submission
                  </CardDescription>
                </CardHeader>
                <CardContent>
                <div className="space-y-4">
                <div>
                  <label htmlFor="teaching-rating-review" className="text-sm font-medium">Teaching Rating (0-2)</label>
                  <Select
                    value={ratings.teaching.toString()}
                    onValueChange={(value) => handleRatingChange("teaching", value)}
                  >
                    <SelectTrigger>
                          <SelectValue placeholder="Select rating" />
                        </SelectTrigger>
                        <SelectContent>
                      <SelectItem value="0">0 - Unsatisfactory</SelectItem>
                      <SelectItem value="1">1 - Satisfactory</SelectItem>
                      <SelectItem value="2">2 - Excellent</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                <div>
                  <label htmlFor="research-rating-review" className="text-sm font-medium">Research Rating (0-2)</label>
                  <Select
                    value={ratings.research.toString()}
                    onValueChange={(value) => handleRatingChange("research", value)}
                  >
                    <SelectTrigger>
                          <SelectValue placeholder="Select rating" />
                        </SelectTrigger>
                        <SelectContent>
                      <SelectItem value="0">0 - Unsatisfactory</SelectItem>
                      <SelectItem value="1">1 - Satisfactory</SelectItem>
                      <SelectItem value="2">2 - Excellent</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                <div>
                  <label className="text-sm font-medium">Service Rating (0-2)</label>
                  <Select
                    value={ratings.service.toString()}
                    onValueChange={(value) => handleRatingChange("service", value)}
                  >
                    <SelectTrigger>
                          <SelectValue placeholder="Select rating" />
                        </SelectTrigger>
                        <SelectContent>
                      <SelectItem value="0">0 - Unsatisfactory</SelectItem>
                      <SelectItem value="1">1 - Satisfactory</SelectItem>
                      <SelectItem value="2">2 - Excellent</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                <div>
                  <label className="text-sm font-medium">Comments</label>
                      <Textarea
                    value={ratings.comments}
                    onChange={(e) => handleCommentsChange(e.target.value)}
                    placeholder="Enter your comments here..."
                  />
                </div>

                <Button
                  className="w-full"
                  onClick={() => handleSubmitRating(selectedSubmission.id, ratings.teaching)}
                  disabled={!ratings.teaching || !ratings.research || !ratings.service}
                >
                  Submit Rating
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
