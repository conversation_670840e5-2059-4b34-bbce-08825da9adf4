"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { toast } from "sonner";
import { Loader2, <PERSON>R<PERSON>, ArrowLeft } from "lucide-react";
import { Input } from "@/components/ui/input";

interface CommitteeMember {
  id: number;
  faculty_id: number;
  faculty_name: string;
  created_at: string;
}

interface FacultyMember {
  faculty_id: number;
  first_name: string;
  last_name: string;
  full_name: string;
  work_email: string;
}

export default function CommitteeManagement() {
  const { data: session } = useSession();
  const [loading, setLoading] = useState(true);
  const [committeeMembers, setCommitteeMembers] = useState<CommitteeMember[]>([]);
  const [regularFaculty, setRegularFaculty] = useState<FacultyMember[]>([]);
  const [unitId, setUnitId] = useState<number | null>(null);
  const [unitName, setUnitName] = useState<string>("");
  const [committeeName, setCommitteeName] = useState<string>("");
  const [isAddingMember, setIsAddingMember] = useState(false);
  const [isRemovingMember, setIsRemovingMember] = useState(false);
  const [selectedFacultyId, setSelectedFacultyId] = useState<number | null>(null);
  const [selectedCommitteeMemberId, setSelectedCommitteeMemberId] = useState<number | null>(null);
  const [searchFaculty, setSearchFaculty] = useState("");
  const [searchCommittee, setSearchCommittee] = useState("");

  useEffect(() => {
    fetchCommitteeData();
  }, []);

  const fetchCommitteeData = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/merit-review/committee/department");

      if (!response.ok) {
        throw new Error("Failed to fetch committee data");
      }

      const data = await response.json();
      setCommitteeMembers(data.committeeMembers || []);
      setRegularFaculty(data.regularFaculty || []);
      setUnitId(data.unitId);
      setUnitName(data.unitName);
      setCommitteeName(data.committeeName);
    } catch (error) {
      console.error("Error fetching committee data:", error);
      toast.error("Failed to load committee data");
    } finally {
      setLoading(false);
    }
  };

  const addCommitteeMember = async (facultyId: number) => {
    if (!facultyId || !unitId) {
      toast.error("Please select a faculty member");
      return;
    }

    try {
      setIsAddingMember(true);
      const response = await fetch("/api/merit-review/committee/department", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          unit_id: unitId,
          faculty_id: facultyId,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to add committee member");
      }

      const newMember = await response.json();
      setCommitteeMembers([...committeeMembers, newMember]);
      setSelectedFacultyId(null);
      toast.success("Committee member added successfully");
    } catch (error) {
      console.error("Error adding committee member:", error);
      toast.error(error instanceof Error ? error.message : "Failed to add committee member");
    } finally {
      setIsAddingMember(false);
    }
  };

  const removeCommitteeMember = async (id: number) => {
    try {
      setIsRemovingMember(true);
      const response = await fetch(`/api/merit-review/committee/department?id=${id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to remove committee member");
      }

      setCommitteeMembers(committeeMembers.filter((member) => member.id !== id));
      setSelectedCommitteeMemberId(null);
      toast.success("Committee member removed successfully");
    } catch (error) {
      console.error("Error removing committee member:", error);
      toast.error(error instanceof Error ? error.message : "Failed to remove committee member");
    } finally {
      setIsRemovingMember(false);
    }
  };

  // Filter out faculty members who are already in the committee
  const availableFaculty = regularFaculty.filter(
    (faculty) => !committeeMembers.some((member) => member.faculty_id === faculty.faculty_id)
  );

  // Filter faculty members based on search
  const filteredFaculty = availableFaculty.filter(
    (faculty) =>
      faculty.full_name.toLowerCase().includes(searchFaculty.toLowerCase()) ||
      faculty.work_email.toLowerCase().includes(searchFaculty.toLowerCase())
  );

  // Filter committee members based on search
  const filteredCommitteeMembers = committeeMembers.filter(
    (member) => member.faculty_name.toLowerCase().includes(searchCommittee.toLowerCase())
  );

  if (loading) {
    return (
      <div className="flex justify-center items-center h-32">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-2">
        <h2 className="text-lg font-semibold">{committeeName}</h2>
        <p className="text-muted-foreground text-sm">
          Select faculty members from your department to serve on the merit review committee.
          Double-click a name or use the arrow buttons to move faculty members between lists.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-7 gap-4">
        {/* Left Column - Available Faculty */}
        <div className="md:col-span-3">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Available Faculty Members</CardTitle>
              <Input
                placeholder="Search faculty..."
                value={searchFaculty}
                onChange={(e) => setSearchFaculty(e.target.value)}
                className="mt-2"
              />
            </CardHeader>
            <CardContent>
              <div className="border rounded-md h-[300px] overflow-y-auto">
                {filteredFaculty.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    No available faculty members
                  </div>
                ) : (
                  <ul className="divide-y">
                    {filteredFaculty.map((faculty) => (
                      <li
                        key={faculty.faculty_id}
                        className={`px-4 py-2 cursor-pointer hover:bg-muted transition-colors ${
                          selectedFacultyId === faculty.faculty_id ? 'bg-muted' : ''
                        }`}
                        onClick={() => setSelectedFacultyId(faculty.faculty_id)}
                        onDoubleClick={() => addCommitteeMember(faculty.faculty_id)}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter' || e.key === ' ') {
                            e.preventDefault();
                            setSelectedFacultyId(faculty.faculty_id);
                          }
                        }}
                        tabIndex={0}
                        role="option"
                        aria-label={`Select faculty member ${faculty.first_name} ${faculty.last_name}`}
                      >
                        <div className="font-medium">{faculty.full_name}</div>
                        <div className="text-xs text-muted-foreground">{faculty.work_email}</div>
                      </li>
                    ))}
                  </ul>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Middle Column - Arrows */}
        <div className="md:col-span-1 flex flex-row md:flex-col justify-center items-center gap-4">
          <Button
            variant="outline"
            size="icon"
            disabled={selectedFacultyId === null || isAddingMember}
            onClick={() => {
              if (selectedFacultyId !== null) {
                addCommitteeMember(selectedFacultyId);
              }
            }}
          >
            {isAddingMember ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <ArrowRight className="h-4 w-4" />
            )}
          </Button>
          <Button
            variant="outline"
            size="icon"
            disabled={selectedCommitteeMemberId === null || isRemovingMember}
            onClick={() => {
              if (selectedCommitteeMemberId !== null) {
                removeCommitteeMember(selectedCommitteeMemberId);
              }
            }}
          >
            {isRemovingMember ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <ArrowLeft className="h-4 w-4" />
            )}
          </Button>
        </div>

        {/* Right Column - Committee Members */}
        <div className="md:col-span-3">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Committee Members</CardTitle>
              <Input
                placeholder="Search committee members..."
                value={searchCommittee}
                onChange={(e) => setSearchCommittee(e.target.value)}
                className="mt-2"
              />
            </CardHeader>
            <CardContent>
              <div className="border rounded-md h-[300px] overflow-y-auto">
                {filteredCommitteeMembers.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    No committee members added yet
                  </div>
                ) : (
                  <ul className="divide-y">
                    {filteredCommitteeMembers.map((member) => (
                      <li
                        key={member.id}
                        className={`px-4 py-2 cursor-pointer hover:bg-muted transition-colors ${
                          selectedCommitteeMemberId === member.id ? 'bg-muted' : ''
                        }`}
                        onClick={() => setSelectedCommitteeMemberId(member.id)}
                        onDoubleClick={() => removeCommitteeMember(member.id)}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter' || e.key === ' ') {
                            e.preventDefault();
                            setSelectedCommitteeMemberId(member.id);
                          }
                        }}
                        tabIndex={0}
                        role="option"
                        aria-label={`Select committee member ${member.faculty_name}`}
                      >
                        <div className="font-medium">{member.faculty_name}</div>
                      </li>
                    ))}
                  </ul>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
