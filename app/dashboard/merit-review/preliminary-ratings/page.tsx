"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { toast } from "sonner";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Loader2, FileText, CheckCircle2, Clock } from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";

interface Submission {
  id: number;
  faculty_id: number;
  faculty_name: string;
  unit_id: number;
  unit_name: string;
  create_dt: string;
  update_dt: string;
  submit_dt?: string;
  status: string;
  rating_status: string;
  rating_id?: number;
  teaching_rating?: number;
  research_rating?: number;
  service_rating?: number;
  comments?: string;
  rating_created_at?: string;
  rating_updated_at?: string;
  is_submitted?: boolean;
}

interface RatingForm {
  teaching: number;
  research: number;
  service: number;
  comments: string;
  is_submitted: boolean;
}

export default function PreliminaryRatingsPage() {
  const { data: session, status: authStatus } = useSession();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [submissions, setSubmissions] = useState<Submission[]>([]);
  const [selectedSubmission, setSelectedSubmission] = useState<Submission | null>(null);
  const [ratings, setRatings] = useState<RatingForm>({
    teaching: 0,
    research: 0,
    service: 0,
    comments: "",
    is_submitted: false
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Redirect if not authenticated
  useEffect(() => {
    if (authStatus === "unauthenticated") {
      router.push("/login");
    }
  }, [authStatus, router]);

  // Fetch submissions
  const fetchSubmissions = async () => {
    try {
      setIsLoading(true);
      const response = await fetch("/api/merit-review/preliminary-ratings");
      if (!response.ok) {
        throw new Error("Failed to fetch submissions");
      }
      const data = await response.json();
      setSubmissions(data);
    } catch (error) {
      console.error("Error fetching submissions:", error);
      toast.error("Failed to fetch submissions");
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch initial data
  useEffect(() => {
    if (authStatus === "authenticated") {
      fetchSubmissions();
    }
  }, [authStatus]);

  const handleSelectSubmission = (submission: Submission) => {
    setSelectedSubmission(submission);

    // Initialize rating form with existing values or defaults
    setRatings({
      teaching: submission.teaching_rating !== undefined ? submission.teaching_rating : 0,
      research: submission.research_rating !== undefined ? submission.research_rating : 0,
      service: submission.service_rating !== undefined ? submission.service_rating : 0,
      comments: submission.comments || "",
      is_submitted: false
    });
  };

  const handleRatingChange = (category: keyof RatingForm, value: string | boolean) => {
    setRatings(prev => ({
      ...prev,
      [category]: typeof value === 'string' ? parseInt(value) : value
    }));
  };

  const handleSaveRating = async (isSubmitted: boolean = false) => {
    if (!selectedSubmission) return;

    try {
      setIsSubmitting(true);
      const response = await fetch("/api/merit-review/preliminary-ratings", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          report_id: selectedSubmission.id,
          teaching_rating: ratings.teaching,
          research_rating: ratings.research,
          service_rating: ratings.service,
          comments: ratings.comments,
          is_submitted: isSubmitted
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to save rating");
      }

      toast.success(isSubmitted ? "Preliminary rating submitted successfully" : "Preliminary rating saved successfully");

      // Refresh submissions to get updated ratings
      fetchSubmissions();
      setSelectedSubmission(null);
    } catch (error) {
      console.error("Error saving preliminary rating:", error);
      toast.error(error instanceof Error ? error.message : "Failed to save preliminary rating");
    } finally {
      setIsSubmitting(false);
    }
  };

  const getRatingStatusBadge = (status: string) => {
    switch (status) {
      case 'not_started':
        return <Badge variant="outline">Not Started</Badge>;
      case 'in_progress':
        return <Badge variant="secondary">In Progress</Badge>;
      case 'submitted':
        return <Badge className="bg-green-500 hover:bg-green-600">Submitted</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A';
    return format(new Date(dateString), 'MMM d, yyyy');
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Preliminary Ratings</h1>
      </div>

      {isLoading ? (
        <div className="flex justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>Submissions to Rate</CardTitle>
                <CardDescription>
                  Select a submission to provide preliminary ratings
                </CardDescription>
              </CardHeader>
              <CardContent>
                {submissions.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    No submissions available for preliminary rating
                  </div>
                ) : (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Faculty</TableHead>
                        <TableHead>Unit</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Created</TableHead>
                        <TableHead>Updated</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {submissions.map((submission) => (
                        <TableRow key={submission.id}>
                          <TableCell>{submission.faculty_name}</TableCell>
                          <TableCell>{submission.unit_name}</TableCell>
                          <TableCell>{getRatingStatusBadge(submission.rating_status)}</TableCell>
                          <TableCell>{formatDate(submission.create_dt)}</TableCell>
                          <TableCell>{formatDate(submission.update_dt)}</TableCell>
                          <TableCell>
                            <div className="flex space-x-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => router.push(`/dashboard/merit-review/submission/v2/admin?id=${submission.id}&faculty=${encodeURIComponent(submission.faculty_name)}`)}
                              >
                                <FileText className="h-4 w-4 mr-1" />
                                View
                              </Button>
                              {submission.rating_status !== 'submitted' && (
                                <Button
                                  size="sm"
                                  onClick={() => handleSelectSubmission(submission)}
                                >
                                  Rate
                                </Button>
                              )}
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                )}
              </CardContent>
            </Card>
          </div>

          {selectedSubmission && (
            <Card>
              <CardHeader>
                <CardTitle>Submit Preliminary Rating</CardTitle>
                <CardDescription>
                  Rate {selectedSubmission.faculty_name}&apos;s submission
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <label htmlFor="teaching-rating" className="text-sm font-medium">Teaching Rating (0-2)</label>
                    <Select
                      value={ratings.teaching.toString()}
                      onValueChange={(value) => handleRatingChange("teaching", value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select rating" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="0">0 - Unsatisfactory</SelectItem>
                        <SelectItem value="1">1 - Satisfactory</SelectItem>
                        <SelectItem value="2">2 - Excellent</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <label htmlFor="research-rating" className="text-sm font-medium">Research Rating (0-2)</label>
                    <Select
                      value={ratings.research.toString()}
                      onValueChange={(value) => handleRatingChange("research", value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select rating" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="0">0 - Unsatisfactory</SelectItem>
                        <SelectItem value="1">1 - Satisfactory</SelectItem>
                        <SelectItem value="2">2 - Excellent</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <label htmlFor="service-rating" className="text-sm font-medium">Service Rating (0-2)</label>
                    <Select
                      value={ratings.service.toString()}
                      onValueChange={(value) => handleRatingChange("service", value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select rating" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="0">0 - Unsatisfactory</SelectItem>
                        <SelectItem value="1">1 - Satisfactory</SelectItem>
                        <SelectItem value="2">2 - Excellent</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <label htmlFor="comments" className="text-sm font-medium">Comments</label>
                    <Textarea
                      id="comments"
                      value={ratings.comments}
                      onChange={(e) => handleRatingChange("comments", e.target.value)}
                      placeholder="Enter your comments here..."
                    />
                  </div>

                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      className="flex-1"
                      onClick={() => handleSaveRating(false)}
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <Clock className="h-4 w-4 mr-2" />}
                      Save Draft
                    </Button>
                    <Button
                      className="flex-1"
                      onClick={() => handleSaveRating(true)}
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <CheckCircle2 className="h-4 w-4 mr-2" />}
                      Submit Rating
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      )}
    </div>
  );
}
