"use client";

import { useState, useEffect } from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

interface AdvertisementFormProps {
  data: {
    position_title: string;
    advertisement_body: string;
  };
  onChange: (updates: any) => void;
  errors: Record<string, string>;
}

export default function AdvertisementForm({ data, onChange, errors }: AdvertisementFormProps) {
  const [wordCount, setWordCount] = useState(0);

  useEffect(() => {
    // Count words in advertisement body
    const words = data.advertisement_body.trim().split(/\s+/).filter(word => word.length > 0);
    setWordCount(words.length);
  }, [data.advertisement_body]);

  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange({ position_title: e.target.value });
  };

  const handleBodyChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    onChange({ advertisement_body: e.target.value });
  };

  const insertTemplate = () => {
    const template = `The Department of [DEPARTMENT NAME] at the University of Waterloo invites applications for a [TENURE-TRACK/TEACHING STREAM/RESEARCH] faculty position at the [RANK] level in [AREA OF SPECIALIZATION].

QUALIFICATIONS:
• PhD in [RELEVANT FIELD] or closely related discipline
• [ADDITIONAL QUALIFICATIONS]

RESPONSIBILITIES:
• Teaching undergraduate and graduate courses
• Conducting independent research
• Supervising graduate students
• Contributing to service activities

The University of Waterloo is committed to advancing equity and inclusion. We welcome applications from all qualified candidates, particularly those who identify as women, Indigenous peoples, Black, racialized, or 2SLGBTQ+ individuals, and persons with disabilities.

APPLICATION REQUIREMENTS:
• Cover letter
• Curriculum vitae
• Research statement
• Teaching statement
• Three letters of reference

Applications should be submitted through [APPLICATION PORTAL] by [DEADLINE DATE].

For more information about this position, please contact [CONTACT INFORMATION].`;

    onChange({ advertisement_body: template });
  };

  return (
    <div className="space-y-6">
      {/* Position Title */}
      <div className="space-y-2">
        <Label htmlFor="position-title" className="text-base font-medium">
          Position Title *
        </Label>
        <Input
          id="position-title"
          value={data.position_title}
          onChange={handleTitleChange}
          placeholder="e.g., Assistant Professor in Chemical Engineering"
          className={`text-lg ${errors.position_title ? "border-red-500" : ""}`}
        />
        {errors.position_title && (
          <p className="text-red-500 text-sm">{errors.position_title}</p>
        )}
        <p className="text-sm text-gray-600">
          This title will appear in the job posting and official documents
        </p>
      </div>

      {/* Advertisement Body */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <Label htmlFor="advertisement-body" className="text-base font-medium">
            Body of Advertisement *
          </Label>
          <div className="flex items-center space-x-4">
            <span className={`text-sm ${wordCount > 1000 ? 'text-red-500' : 'text-gray-500'}`}>
              {wordCount} / 1000 words
            </span>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={insertTemplate}
            >
              Insert Template
            </Button>
          </div>
        </div>
        
        <Textarea
          id="advertisement-body"
          value={data.advertisement_body}
          onChange={handleBodyChange}
          placeholder="Enter the complete job advertisement text..."
          rows={20}
          className={`font-mono text-sm ${errors.advertisement_body ? "border-red-500" : ""}`}
        />
        
        {errors.advertisement_body && (
          <p className="text-red-500 text-sm">{errors.advertisement_body}</p>
        )}
        
        <div className="flex items-center justify-between text-sm text-gray-600">
          <span>Maximum 1,000 words</span>
          {wordCount > 1000 && (
            <span className="text-red-500 font-medium">
              Exceeds word limit by {wordCount - 1000} words
            </span>
          )}
        </div>
      </div>

      {/* Preview Card */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Advertisement Preview</CardTitle>
          <CardDescription>
            This is how your advertisement will appear to applicants
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h3 className="text-xl font-bold text-gray-900">
                {data.position_title || "Position Title"}
              </h3>
              <p className="text-sm text-gray-600 mt-1">
                University of Waterloo - Faculty of Engineering
              </p>
            </div>
            
            <div className="prose prose-sm max-w-none">
              {data.advertisement_body ? (
                <div className="whitespace-pre-wrap text-gray-700">
                  {data.advertisement_body}
                </div>
              ) : (
                <p className="text-gray-400 italic">
                  Advertisement content will appear here...
                </p>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Guidelines */}
      <Card className="bg-blue-50 border-blue-200">
        <CardHeader>
          <CardTitle className="text-lg text-blue-900">Advertisement Guidelines</CardTitle>
        </CardHeader>
        <CardContent className="text-sm text-blue-800">
          <ul className="space-y-2">
            <li>• <strong>Position Title:</strong> Should clearly indicate the rank, department, and area of specialization</li>
            <li>• <strong>Qualifications:</strong> List required and preferred qualifications, including education and experience</li>
            <li>• <strong>Responsibilities:</strong> Outline teaching, research, and service expectations</li>
            <li>• <strong>Application Requirements:</strong> Specify required documents and submission process</li>
            <li>• <strong>Equity Statement:</strong> Include the University&apos;s commitment to equity and inclusion</li>
            <li>• <strong>Contact Information:</strong> Provide contact details for inquiries</li>
            <li>• <strong>Deadline:</strong> Clearly state the application deadline</li>
          </ul>
        </CardContent>
      </Card>

      {/* Required Elements Checklist */}
      <Card className="bg-gray-50 border-gray-200">
        <CardHeader>
          <CardTitle className="text-lg text-gray-900">Required Elements Checklist</CardTitle>
          <CardDescription>
            Ensure your advertisement includes these mandatory elements
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            {[
              "Position rank and department",
              "Area of specialization",
              "Required qualifications",
              "Teaching responsibilities",
              "Research expectations",
              "Application deadline",
              "Required documents",
              "Submission instructions",
              "Equity and inclusion statement",
              "Contact information"
            ].map((item, index) => (
              <div key={index} className="flex items-center space-x-2">
                <div className={`w-4 h-4 rounded border-2 flex items-center justify-center ${
                  data.advertisement_body.toLowerCase().includes(item.split(' ')[0].toLowerCase())
                    ? 'bg-green-500 border-green-500'
                    : 'border-gray-300'
                }`}>
                  {data.advertisement_body.toLowerCase().includes(item.split(' ')[0].toLowerCase()) && (
                    <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  )}
                </div>
                <span className={
                  data.advertisement_body.toLowerCase().includes(item.split(' ')[0].toLowerCase())
                    ? 'text-green-700'
                    : 'text-gray-600'
                }>
                  {item}
                </span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
