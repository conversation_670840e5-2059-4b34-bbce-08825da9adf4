"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { CheckCircle, AlertCircle } from "lucide-react";

interface ReviewSubmitFormProps {
  data: {
    // Hiring Units
    is_joint_appointment: boolean;
    home_department_id: number | null;
    home_department_percentage: number;
    second_department_id: number | null;
    second_department_percentage: number | null;
    
    // Position Details
    career_path: 'tenure_stream' | 'teaching_stream' | 'research_faculty' | null;
    position_type: 'new_position' | 'replacement' | null;
    new_position_type: 'addition_to_operating_complement' | 'not_in_complement' | null;
    hiring_plan_reference: string;
    existing_position_number: string;
    replacement_reason: 'resignation' | 'retirement' | 'termination' | 'death' | 'other' | null;
    replacement_reason_other: string;
    incumbent_name: string;
    incumbent_employee_id: string;
    termination_date: string;
    is_bridge_position: boolean;
    bridge_position_number: string;
    bridge_end_date: string;
    funding_sources: string;
    position_notes: string;
    
    // Advertisement
    position_title: string;
    advertisement_body: string;
  };
  errors: Record<string, string>;
}

export default function ReviewSubmitForm({ data, errors }: ReviewSubmitFormProps) {
  // Mock department names for display
  const getDepartmentName = (id: number | null) => {
    const departments: Record<number, string> = {
      1: "Chemical Engineering",
      2: "Civil and Environmental Engineering", 
      3: "Electrical and Computer Engineering",
      4: "Management Sciences",
      5: "Mechanical and Mechatronics Engineering",
      6: "Systems Design Engineering",
    };
    return id ? departments[id] || `Department ${id}` : '';
  };

  const formatCareerPath = (path: string | null) => {
    const paths: Record<string, string> = {
      'tenure_stream': 'Tenure Stream',
      'teaching_stream': 'Teaching Stream',
      'research_faculty': 'Research Faculty'
    };
    return path ? paths[path] : '';
  };

  const formatPositionType = (type: string | null) => {
    const types: Record<string, string> = {
      'new_position': 'New Position',
      'replacement': 'Replacement for Permanent Loss'
    };
    return type ? types[type] : '';
  };

  const formatNewPositionType = (type: string | null) => {
    const types: Record<string, string> = {
      'addition_to_operating_complement': 'Addition to Operating Complement',
      'not_in_complement': 'Not in Complement'
    };
    return type ? types[type] : '';
  };

  const formatReplacementReason = (reason: string | null) => {
    const reasons: Record<string, string> = {
      'resignation': 'Resignation',
      'retirement': 'Retirement',
      'termination': 'Termination',
      'death': 'Death',
      'other': 'Other'
    };
    return reason ? reasons[reason] : '';
  };

  const hasErrors = Object.keys(errors).length > 0;

  return (
    <div className="space-y-6">
      {/* Validation Status */}
      <Card className={hasErrors ? "border-red-200 bg-red-50" : "border-green-200 bg-green-50"}>
        <CardContent className="pt-6">
          <div className="flex items-center space-x-2">
            {hasErrors ? (
              <AlertCircle className="h-5 w-5 text-red-500" />
            ) : (
              <CheckCircle className="h-5 w-5 text-green-500" />
            )}
            <span className={`font-medium ${hasErrors ? 'text-red-700' : 'text-green-700'}`}>
              {hasErrors ? 'Please fix the following errors before submitting:' : 'All required information is complete'}
            </span>
          </div>
          {hasErrors && (
            <ul className="mt-3 text-sm text-red-600 space-y-1">
              {Object.entries(errors).map(([field, error]) => (
                <li key={field}>• {error}</li>
              ))}
            </ul>
          )}
        </CardContent>
      </Card>

      {/* Hiring Units Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Hiring Unit(s)</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center space-x-2">
            <Badge variant={data.is_joint_appointment ? "default" : "secondary"}>
              {data.is_joint_appointment ? "Joint Appointment" : "Single Department"}
            </Badge>
          </div>
          
          <div className="space-y-3">
            <div>
              <h4 className="font-medium">Home Department</h4>
              <p className="text-gray-600">{getDepartmentName(data.home_department_id)}</p>
              <p className="text-sm text-gray-500">{data.home_department_percentage}% of position</p>
            </div>
            
            {data.is_joint_appointment && data.second_department_id && (
              <div>
                <h4 className="font-medium">Second Department</h4>
                <p className="text-gray-600">{getDepartmentName(data.second_department_id)}</p>
                <p className="text-sm text-gray-500">{data.second_department_percentage}% of position</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Position Details Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Position Details</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium">Career Path</h4>
              <p className="text-gray-600">{formatCareerPath(data.career_path)}</p>
            </div>
            <div>
              <h4 className="font-medium">Position Type</h4>
              <p className="text-gray-600">{formatPositionType(data.position_type)}</p>
            </div>
          </div>

          {data.position_type === 'new_position' && (
            <div className="space-y-3 p-3 bg-blue-50 rounded">
              <h4 className="font-medium">New Position Details</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium">Type:</span>
                  <p className="text-gray-600">{formatNewPositionType(data.new_position_type)}</p>
                </div>
                <div>
                  <span className="font-medium">Hiring Plan Reference:</span>
                  <p className="text-gray-600">{data.hiring_plan_reference || 'Not specified'}</p>
                </div>
              </div>
            </div>
          )}

          {data.position_type === 'replacement' && (
            <div className="space-y-3 p-3 bg-yellow-50 rounded">
              <h4 className="font-medium">Replacement Position Details</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium">Position Number:</span>
                  <p className="text-gray-600">{data.existing_position_number || 'Not specified'}</p>
                </div>
                <div>
                  <span className="font-medium">Replacement Reason:</span>
                  <p className="text-gray-600">
                    {formatReplacementReason(data.replacement_reason)}
                    {data.replacement_reason === 'other' && data.replacement_reason_other && 
                      ` - ${data.replacement_reason_other}`
                    }
                  </p>
                </div>
                {data.incumbent_name && (
                  <div>
                    <span className="font-medium">Incumbent:</span>
                    <p className="text-gray-600">{data.incumbent_name}</p>
                  </div>
                )}
                {data.termination_date && (
                  <div>
                    <span className="font-medium">Termination Date:</span>
                    <p className="text-gray-600">{data.termination_date}</p>
                  </div>
                )}
                {data.is_bridge_position && (
                  <div className="col-span-2">
                    <span className="font-medium">Bridge Position:</span>
                    <p className="text-gray-600">
                      Yes - {data.bridge_position_number} 
                      {data.bridge_end_date && ` (ends ${data.bridge_end_date})`}
                    </p>
                  </div>
                )}
              </div>
            </div>
          )}

          {data.funding_sources && (
            <div>
              <h4 className="font-medium">Funding Sources</h4>
              <p className="text-gray-600 text-sm whitespace-pre-wrap">{data.funding_sources}</p>
            </div>
          )}

          {data.position_notes && (
            <div>
              <h4 className="font-medium">Position Notes</h4>
              <p className="text-gray-600 text-sm whitespace-pre-wrap">{data.position_notes}</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Advertisement Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Advertisement</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h4 className="font-medium">Position Title</h4>
            <p className="text-gray-900 text-lg">{data.position_title || 'Not specified'}</p>
          </div>
          
          <Separator />
          
          <div>
            <h4 className="font-medium mb-2">Advertisement Body</h4>
            <div className="bg-gray-50 p-4 rounded border max-h-64 overflow-y-auto">
              {data.advertisement_body ? (
                <div className="text-sm text-gray-700 whitespace-pre-wrap">
                  {data.advertisement_body}
                </div>
              ) : (
                <p className="text-gray-400 italic">No advertisement content provided</p>
              )}
            </div>
            <p className="text-sm text-gray-500 mt-2">
              Word count: {data.advertisement_body.trim().split(/\s+/).filter(w => w.length > 0).length} / 1000
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Next Steps */}
      <Card className="bg-blue-50 border-blue-200">
        <CardHeader>
          <CardTitle className="text-blue-900">Next Steps</CardTitle>
          <CardDescription className="text-blue-700">
            After submission, your request will enter the approval workflow
          </CardDescription>
        </CardHeader>
        <CardContent className="text-sm text-blue-800">
          <ol className="space-y-2">
            <li>1. <strong>Unit Head Review:</strong> Your unit head will review and approve the request</li>
            <li>2. <strong>Faculty Office Review:</strong> Donna (Director of Integrated Planning) will review</li>
            <li>3. <strong>Faculty Services Review:</strong> Veronica (Director of Faculty Services) will review</li>
            <li>4. <strong>Dean Approval:</strong> The Dean will review and approve the request</li>
            <li>5. <strong>Form Generation:</strong> Mission Critical and Authorization forms will be generated</li>
            <li>6. <strong>Provost Approval:</strong> Final approval from the Provost&apos;s office</li>
            <li>7. <strong>Distribution:</strong> Approval package distributed to all stakeholders</li>
          </ol>
          <p className="mt-4 font-medium">
            You will receive email notifications at each step of the process.
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
